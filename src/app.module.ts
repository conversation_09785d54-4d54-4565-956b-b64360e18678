import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async () => {
        const uri =
          'mongodb+srv://magnetadminapi:<EMAIL>/?retryWrites=true&w=majority&appName=magnet-admin-api';

        return {
          uri,
          onConnectionCreate: (connection) => {
            connection.on('connected', () => {
              console.log('MongoDB เชื่อมต่อสำเร็จแล้ว!');
            });

            connection.on('error', (error) => {
              console.error('MongoDB เชื่อมต่อไม่สำเร็จ:', error.message);
            });

            connection.on('disconnected', () => {
              console.log('MongoDB ตัดการเชื่อมต่อแล้ว');
            });

            return connection;
          },
        };
      },
    }),
    AuthModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
