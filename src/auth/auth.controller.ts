import { Controller, Post, Body, HttpCode, HttpStatus, UnauthorizedException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';

@Controller('auth') // กำหนด Base Path เป็น /auth
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login') // สำหรับ POST /auth/login
  @HttpCode(HttpStatus.OK) // กำหนด Status Code เมื่อสำเร็จเป็น 200 OK
  async login(@Body() loginDto: LoginDto) {
    const adminUser = await this.authService.validateAdminUser(loginDto);

    if (!adminUser) {
      // ถ้า validation ไม่ผ่าน ให้ throw UnauthorizedException
      throw new UnauthorizedException('Invalid credentials.');
    }

    // ตรงนี้สามารถคืนค่าข้อมูลผู้ใช้ หรือ JWT Token กลับไปให้ Web Admin
    // สำหรับตอนนี้ เราจะคืนค่า user object (โดยไม่มี password)
    return {
      message: 'Login successful',
      user: adminUser,
      // token: 'your_jwt_token_here' // เพิ่มในอนาคต
    };
  }

  // อาจจะเพิ่ม Endpoint สำหรับสร้าง Admin user ครั้งแรก
  @Post('register-admin') // สำหรับ POST /auth/register-admin (ใช้สำหรับตั้งค่าเริ่มต้นเท่านั้น)
  @HttpCode(HttpStatus.CREATED)
  async registerAdmin(@Body() loginDto: LoginDto) {
    const newAdmin = await this.authService.createAdminUser(loginDto.username, loginDto.password);
    return {
      message: 'Admin user created successfully',
      user: newAdmin,
    };
  }
}