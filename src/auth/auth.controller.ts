import { Controller, Get, Post, Body, HttpCode, HttpStatus, UnauthorizedException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RegisterAdminDto } from './dto/register-admin.dto';

@Controller('auth') // กำหนด Base Path เป็น /auth
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Get() // สำหรับ GET /auth - แสดงข้อมูล endpoints ที่มี
  getAuthInfo() {
    return {
      message: 'Authentication API',
      endpoints: {
        login: {
          method: 'POST',
          path: '/auth/login',
          description: 'Login with username and password',
          body: {
            username: 'string',
            password: 'string'
          }
        },
        registerAdmin: {
          method: 'POST',
          path: '/auth/register-admin',
          description: 'Register new admin user',
          body: {
            username: 'string',
            password: 'string',
            confirmPassword: 'string',
            name: 'string',
            surname: 'string',
            nickname: 'string (optional)',
            phoneNumber: 'string (optional)',
            email: 'string',
            type: 'string'
          }
        }
      }
    };
  }

  @Post('login') // สำหรับ POST /auth/login
  @HttpCode(HttpStatus.OK) // กำหนด Status Code เมื่อสำเร็จเป็น 200 OK
  async login(@Body() loginDto: LoginDto) {
    try {
      const result = await this.authService.login(loginDto);

      return {
        message: 'Login successful',
        access_token: result.access_token,
        user: result.user,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid credentials.');
    }
  }

  // อาจจะเพิ่ม Endpoint สำหรับสร้าง Admin user ครั้งแรก
  @Post('register-admin') // สำหรับ POST /auth/register-admin (ใช้สำหรับตั้งค่าเริ่มต้นเท่านั้น)
  @HttpCode(HttpStatus.CREATED)
  async registerAdmin(@Body() registerDto: RegisterAdminDto) {
    const newAdmin = await this.authService.createAdminUser(registerDto);
    return {
      message: 'Admin user created successfully',
      user: newAdmin,
    };
  }
}