import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtStrategy } from './jwt.strategy';
import { AdminUser, AdminUserSchema } from './schemas/admin-user.schema'; // Import schema
import { TypesModule } from '../types/types.module'; // Import TypesModule

@Module({
  imports: [
    MongooseModule.forFeature([{ name: AdminUser.name, schema: AdminUserSchema }]), // ลงทะเบียน AdminUserSchema
    TypesModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET') || 'your-secret-key',
        signOptions: { expiresIn: '24h' }, // Token หมดอายุใน 24 ชั่วโมง
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [AuthService, JwtStrategy], // เพิ่ม JwtStrategy
  controllers: [AuthController],
  exports: [AuthService, MongooseModule], // อาจจะ export AuthService ถ้าต้องการใช้ใน Module อื่น
})
export class AuthModule {}