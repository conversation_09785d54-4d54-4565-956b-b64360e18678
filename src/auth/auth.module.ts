import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { AdminUser, AdminUserSchema } from './schemas/admin-user.schema'; // Import schema

@Module({
  imports: [
    MongooseModule.forFeature([{ name: AdminUser.name, schema: AdminUserSchema }]), // ลงทะเบียน AdminUserSchema
  ],
  providers: [AuthService],
  controllers: [AuthController],
  exports: [AuthService, MongooseModule], // อาจจะ export AuthService ถ้าต้องการใช้ใน Module อื่น
})
export class AuthModule {}