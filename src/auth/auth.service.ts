import {
  Injectable,
  ConflictException,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AdminUser, AdminUserDocument } from './schemas/admin-user.schema';
import { LoginDto } from './dto/login.dto';
import { RegisterAdminDto } from './dto/register-admin.dto';
import { TypesService } from '../types/types.service'; // Import TypesService

// Type สำหรับ AdminUser ที่ไม่มี password field
type SafeAdminUser = Omit<AdminUser, 'password' | 'comparePassword'>;

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(AdminUser.name)
    private adminUserModel: Model<AdminUserDocument>,
    private readonly typesService: TypesService, // Inject TypesService
  ) {}

  async createAdminUser(registerDto: RegisterAdminDto): Promise<AdminUser> {
    const {
      username,
      password,
      name,
      surname,
      nickname,
      phoneNumber,
      email,
      type,
    } = registerDto;

    // ตรวจสอบว่า username ซ้ำหรือไม่
    const existingUserByUsername = await this.adminUserModel
      .findOne({ username })
      .exec();
    if (existingUserByUsername) {
      throw new ConflictException('Username already exists.');
    }

    // ตรวจสอบว่า email ซ้ำหรือไม่
    const existingUserByEmail = await this.adminUserModel
      .findOne({ email })
      .exec();
    if (existingUserByEmail) {
      throw new ConflictException('Email already exists.');
    }

    // **********************************************
    // เพิ่มการตรวจสอบว่า 'type' ที่ส่งมามีอยู่ในฐานข้อมูล Types จริงหรือไม่
    // **********************************************
    try {
      const existingType = await this.typesService.findOneByName(type); // ต้องสร้าง findOneByName ใน TypesService
      if (!existingType) {
        throw new BadRequestException(`User type "${type}" does not exist.`);
      }
    } catch (error) {
      // หาก findOneByName โยน NotFoundException มา ก็จับได้ที่นี่
      if (error instanceof NotFoundException) {
        throw new BadRequestException(`User type "${type}" does not exist.`);
      }
      throw error; // โยน error อื่นๆ ต่อไป
    }

    const newUser = new this.adminUserModel({
      username,
      password,
      name,
      surname,
      nickname,
      phoneNumber,
      email,
      type,
    });

    return newUser.save();
  }

  async validateAdminUser(loginDto: LoginDto): Promise<SafeAdminUser | null> {
    const { username, password } = loginDto;
    const adminUser = await this.adminUserModel.findOne({ username }).exec();

    if (adminUser && (await adminUser.comparePassword(password))) {
      // ใช้ destructuring เพื่อแยก password ออก
      const { password: _, ...userWithoutPassword } = adminUser.toObject();
      return userWithoutPassword as SafeAdminUser;
    }
    return null;
  }
}

// ในอนาคตอาจจะเพิ่มฟังก์ชันสำหรับ generate JWT token ที่นี่
// เช่น async login(loginDto: LoginDto) { ... }
