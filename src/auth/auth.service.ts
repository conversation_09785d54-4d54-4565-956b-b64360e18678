import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AdminUser, AdminUserDocument } from './schemas/admin-user.schema';
import { LoginDto } from './dto/login.dto';

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(AdminUser.name) private adminUserModel: Model<AdminUserDocument>,
  ) {}

  // สร้าง Admin User (สำหรับครั้งแรกเท่านั้น หรือการเพิ่ม Admin ใหม่)
  async createAdminUser(username: string, passwordPlain: string): Promise<AdminUser> {
    // ตรวจสอบว่ามี username นี้อยู่แล้วหรือยัง
    const existingUser = await this.adminUserModel.findOne({ username }).exec();
    if (existingUser) {
      throw new UnauthorizedException('Username already exists.');
    }

    const newUser = new this.adminUserModel({ username, password: passwordPlain });
    return newUser.save(); // Password จะถูก hash โดย pre-save hook ใน schema
  }


  async validateAdminUser(loginDto: LoginDto): Promise<AdminUser | null> {
    const { username, password } = loginDto;
    const adminUser = await this.adminUserModel.findOne({ username }).exec();

    if (adminUser && await adminUser.comparePassword(password)) {
      // ถ้าต้องการไม่คืนค่า password กลับไป
      adminUser.password = undefined; // หรือจะใช้ `.toObject()` แล้ว delete field ก็ได้
      return adminUser;
    }
    return null;
  }

  // ในอนาคตคุณอาจจะเพิ่มฟังก์ชันสำหรับ generate JWT token ที่นี่
  // เช่น async login(loginDto: LoginDto) { ... }
}