import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'class-validator';
import { Match } from './match.decorator';

export class RegisterAdminDto {
  @IsString()
  @IsNotEmpty({ message: 'Username is required.' })
  username: string;

  @IsString()
  @IsNotEmpty({ message: 'Password is required.' })
  @MinLength(6, { message: 'Password must be at least 6 characters long.' })
  password: string;

  @IsString()
  @IsNotEmpty({ message: 'Confirm Password is required.' })
  @Match('password', { message: 'Passwords do not match.' })
  confirmPassword: string;

  @IsString()
  @IsNotEmpty({ message: 'Name is required.' })
  name: string;

  @IsString()
  @IsNotEmpty({ message: 'Surname is required.' })
  surname: string;

  @IsString()
  @IsOptional()
  nickname?: string;

  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @IsString()
  @IsNotEmpty({ message: 'Email is required.' })
  @IsEmail({}, { message: 'Invalid email format.' })
  email: string;

  @IsString()
  @IsNotEmpty({ message: 'Type is required.' })
  // เราจะทำการตรวจสอบ 'type' ใน Service หรือ Controller แทน `@IsIn`
  // เพื่อให้ Type ที่ส่งมาตรงกับ Type ที่มีอยู่ในฐานข้อมูล
  type: string;
}
