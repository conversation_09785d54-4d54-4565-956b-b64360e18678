import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import * as bcrypt from 'bcrypt';

export type AdminUserDocument = HydratedDocument<AdminUser>;

@Schema()
export class AdminUser {
  @Prop({ required: true, unique: true }) // กำหนดให้ username ต้องไม่ซ้ำและจำเป็น
  username: string;

  @Prop({ required: true }) // กำหนดให้ password จำเป็น
  password: string; // ตรงนี้จะเก็บ password ที่ถูก hash แล้ว

  @Prop({ required: true }) // ชื่อจริง
  name: string;

  @Prop({ required: true }) // นามสกุล
  surname: string;

  @Prop() // ชื่อเล่น (ไม่บังคับ)
  nickname?: string;

  @Prop() // เบอร์โทรศัพท์ (ไม่บังคับ)
  phoneNumber?: string;

  @Prop({ required: true, unique: true }) // อีเมล (บังคับและไม่ซ้ำ)
  email: string;

  @Prop({ required: true }) // ประเภทผู้ใช้
  type: string;

  // เพิ่มเมธอดสำหรับเปรียบเทียบรหัสผ่าน
  async comparePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.password);
  }
}

export const AdminUserSchema = SchemaFactory.createForClass(AdminUser);

// เพิ่ม Pre-save Hook เพื่อ Hash Password ก่อนบันทึก
AdminUserSchema.pre('save', async function (next) {
  if (this.isModified('password')) { // ตรวจสอบว่า password มีการเปลี่ยนแปลงหรือไม่
    const salt = await bcrypt.genSalt(10); // สร้าง salt
    this.password = await bcrypt.hash(this.password, salt); // Hash password
  }
  next();
});