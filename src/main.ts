// src/main.ts
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common'; // Import ValidationPipe

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true, // ลบ property ที่ไม่ได้อยู่ใน DTO ออกไป
    forbidNonWhitelisted: true, // โยน error ถ้ามี property ที่ไม่ได้อยู่ใน DTO
    transform: true, // แปลง payload ให้เป็น instance ของ DTO
  })); // เพิ่มบรรทัดนี้

  const port = process.env.PORT || 3333;
  await app.listen(port);
  console.log(`Application is running on: http://localhost:${port}`);
}
bootstrap();