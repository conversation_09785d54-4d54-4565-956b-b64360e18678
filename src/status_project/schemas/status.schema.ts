import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type StatusDocument = HydratedDocument<Status>;

@Schema({ timestamps: true }) // เพิ่ม timestamps
export class Status {
  @Prop({ required: true, unique: true }) // ชื่อ Status ต้องไม่ซ้ำกันและจำเป็น
  name: string;
}

export const StatusSchema = SchemaFactory.createForClass(Status);