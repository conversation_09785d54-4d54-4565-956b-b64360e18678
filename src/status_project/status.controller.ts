import { Controller, Get, Post, Body, HttpCode, HttpStatus, Param, Patch, Delete } from '@nestjs/common';
import { StatusService } from './status.service';
import { CreateStatusDto } from './dto/create-status.dto';
import { Status } from './schemas/status.schema';

@Controller('status') // กำหนด Base Path เป็น /status
export class StatusController {
  constructor(private readonly statusService: StatusService) {}

  @Post() // สำหรับ POST /status
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createStatusDto: CreateStatusDto): Promise<Status> {
    return this.statusService.create(createStatusDto);
  }

  @Get() // สำหรับ GET /status (ดึง Status ทั้งหมด)
  async findAll(): Promise<Status[]> {
    return this.statusService.findAll();
  }

  @Get(':id') // สำหรับ GET /status/:id
  async findOne(@Param('id') id: string): Promise<Status> {
    return this.statusService.findOne(id);
  }

  @Patch(':id') // สำหรับ PATCH /status/:id
  async update(@Param('id') id: string, @Body() updateStatusDto: CreateStatusDto): Promise<Status> {
    return this.statusService.update(id, updateStatusDto);
  }

  @Delete(':id') // สำหรับ DELETE /status/:id
  @HttpCode(HttpStatus.NO_CONTENT) // 204 No Content สำหรับการลบสำเร็จ
  async remove(@Param('id') id: string): Promise<void> {
    return this.statusService.remove(id);
  }
}