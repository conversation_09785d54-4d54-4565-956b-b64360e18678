import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { StatusService } from './status.service';
import { StatusController } from './status.controller';
import { Status, StatusSchema } from './schemas/status.schema'; // Import Schema

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Status.name, schema: StatusSchema }]), // ลงทะเบียน StatusSchema
  ],
  providers: [StatusService],
  controllers: [StatusController],
  exports: [StatusService, MongooseModule], // อาจจะ export StatusService ถ้าต้องการใช้ใน Module อื่น
})
export class StatusModule {}