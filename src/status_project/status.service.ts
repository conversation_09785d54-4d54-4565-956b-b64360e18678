import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Status, StatusDocument } from './schemas/status.schema';
import { CreateStatusDto } from './dto/create-status.dto';

@Injectable()
export class StatusService {
  constructor(@InjectModel(Status.name) private statusModel: Model<StatusDocument>) {}

  async create(createStatusDto: CreateStatusDto): Promise<Status> {
    const { name } = createStatusDto;
    // ตรวจสอบว่าชื่อ Status ซ้ำหรือไม่
    const existingStatus = await this.statusModel.findOne({ name }).exec();
    if (existingStatus) {
      throw new ConflictException(`Status with name "${name}" already exists.`);
    }

    const createdStatus = new this.statusModel(createStatusDto);
    return createdStatus.save();
  }

  async findAll(): Promise<Status[]> {
    return this.statusModel.find().exec();
  }

  async findOne(id: string): Promise<Status> {
    const status = await this.statusModel.findById(id).exec();
    if (!status) {
      throw new NotFoundException(`Status with ID "${id}" not found.`);
    }
    return status;
  }

  // เพิ่มเมธอดสำหรับค้นหาตามชื่อ (จำเป็นสำหรับการตรวจสอบในอนาคต)
  async findOneByName(name: string): Promise<Status | null> {
    return this.statusModel.findOne({ name }).exec();
  }

  // อาจเพิ่มฟังก์ชัน update และ delete ด้วยก็ได้
  async update(id: string, updateStatusDto: CreateStatusDto): Promise<Status> {
    const existingStatus = await this.statusModel.findOne({ name: updateStatusDto.name, _id: { $ne: id } }).exec();
    if (existingStatus) {
      throw new ConflictException(`Status with name "${updateStatusDto.name}" already exists.`);
    }
    const updatedStatus = await this.statusModel.findByIdAndUpdate(id, updateStatusDto, { new: true }).exec();
    if (!updatedStatus) {
      throw new NotFoundException(`Status with ID "${id}" not found.`);
    }
    return updatedStatus;
  }

  async remove(id: string): Promise<void> {
    const result = await this.statusModel.deleteOne({ _id: id }).exec();
    if (result.deletedCount === 0) {
      throw new NotFoundException(`Status with ID "${id}" not found.`);
    }
  }
}