import { <PERSON>, Get, Post, Body, HttpCode, HttpStatus, Param, Patch, Delete } from '@nestjs/common'; // เพิ่ม Param, Patch, Delete
import { TypesService } from './types.service';
import { CreateTypeDto } from './dto/create-type.dto';
import { Type } from './schemas/type.schema';

@Controller('types') // กำหนด Base Path เป็น /types
export class TypesController {
  constructor(private readonly typesService: TypesService) {}

  @Post() // สำหรับ POST /types
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createTypeDto: CreateTypeDto): Promise<Type> {
    return this.typesService.create(createTypeDto);
  }

  @Get() // สำหรับ GET /types (ดึง Type ทั้งหมด)
  async findAll(): Promise<Type[]> {
    return this.typesService.findAll();
  }

  @Get(':id') // สำหรับ GET /types/:id
  async findOne(@Param('id') id: string): Promise<Type> {
    return this.typesService.findOne(id);
  }

  @Patch(':id') // สำหรับ PATCH /types/:id
  async update(@Param('id') id: string, @Body() updateTypeDto: CreateTypeDto): Promise<Type> {
    return this.typesService.update(id, updateTypeDto);
  }

  @Delete(':id') // สำหรับ DELETE /types/:id
  @HttpCode(HttpStatus.NO_CONTENT) // 204 No Content สำหรับการลบสำเร็จ
  async remove(@Param('id') id: string): Promise<void> {
    return this.typesService.remove(id);
  }
}