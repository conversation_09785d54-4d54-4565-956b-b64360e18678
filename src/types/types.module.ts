import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TypesService } from './types.service';
import { TypesController } from './types.controller';
import { Type, TypeSchema } from './schemas/type.schema'; // Import Schema

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Type.name, schema: TypeSchema }]), // ลงทะเบียน TypeSchema
  ],
  providers: [TypesService],
  controllers: [TypesController],
  exports: [TypesService, MongooseModule], // อาจจะ export TypesService ถ้าต้องการใช้ใน Module อื่น
})
export class TypesModule {}