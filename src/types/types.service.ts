import {
  Injectable,
  ConflictException,
  NotFoundException,
} from '@nestjs/common'; // เพิ่ม NotFoundException
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Type, TypeDocument } from './schemas/type.schema';
import { CreateTypeDto } from './dto/create-type.dto';

@Injectable()
export class TypesService {
  constructor(@InjectModel(Type.name) private typeModel: Model<TypeDocument>) {}

  async create(createTypeDto: CreateTypeDto): Promise<Type> {
    const { name } = createTypeDto;
    // ตรวจสอบว่าชื่อ Type ซ้ำหรือไม่
    const existingType = await this.typeModel.findOne({ name }).exec();
    if (existingType) {
      throw new ConflictException(`Type with name "${name}" already exists.`);
    }

    const createdType = new this.typeModel(createTypeDto);
    return createdType.save();
  }

  async findAll(): Promise<Type[]> {
    return this.typeModel.find().exec();
  }

  async findOne(id: string): Promise<Type> {
    const type = await this.typeModel.findById(id).exec();
    if (!type) {
      throw new NotFoundException(`Type with ID "${id}" not found.`);
    }
    return type;
  }

  async findOneByName(name: string): Promise<Type> {
    const type = await this.typeModel.findOne({ name }).exec();
    if (!type) {
      throw new NotFoundException(`Type with name "${name}" not found.`);
    }
    return type;
  }

  async update(id: string, updateTypeDto: CreateTypeDto): Promise<Type> {
    const existingType = await this.typeModel
      .findOne({ name: updateTypeDto.name, _id: { $ne: id } })
      .exec();
    if (existingType) {
      throw new ConflictException(
        `Type with name "${updateTypeDto.name}" already exists.`,
      );
    }
    const updatedType = await this.typeModel
      .findByIdAndUpdate(id, updateTypeDto, { new: true })
      .exec();
    if (!updatedType) {
      throw new NotFoundException(`Type with ID "${id}" not found.`);
    }
    return updatedType;
  }

  async remove(id: string): Promise<void> {
    const result = await this.typeModel.deleteOne({ _id: id }).exec();
    if (result.deletedCount === 0) {
      throw new NotFoundException(`Type with ID "${id}" not found.`);
    }
  }
}
